<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import { gsap } from 'gsap'

const props = defineProps<{
  backgroundColor?: string
}>()

const container = ref<HTMLElement | null>(null)
let scene: THREE.Scene
let camera: THREE.PerspectiveCamera
let renderer: THREE.WebGLRenderer
let controls: OrbitControls
let animationFrameId: number

// 初始化Three.js场景
const initThree = () => {
  if (!container.value) return
  
  // 创建场景
  scene = new THREE.Scene()
  scene.background = new THREE.Color(props.backgroundColor || '#f0f0f0')
  
  // 创建相机
  const width = container.value.clientWidth
  const height = container.value.clientHeight
  camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)
  camera.position.z = 5
  
  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true })
  renderer.setSize(width, height)
  renderer.setPixelRatio(window.devicePixelRatio)
  container.value.appendChild(renderer.domElement)
  
  // 添加轨道控制器
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true
  controls.dampingFactor = 0.05
  
  // 添加环境光
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.5)
  scene.add(ambientLight)
  
  // 添加平行光
  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
  directionalLight.position.set(1, 1, 1)
  scene.add(directionalLight)
  
  // 添加窗口大小变化监听
  window.addEventListener('resize', onWindowResize)
  
  // 开始动画循环
  animate()
}

// 窗口大小变化时更新渲染器和相机
const onWindowResize = () => {
  if (!container.value) return
  
  const width = container.value.clientWidth
  const height = container.value.clientHeight
  
  camera.aspect = width / height
  camera.updateProjectionMatrix()
  
  renderer.setSize(width, height)
}

// 动画循环
const animate = () => {
  animationFrameId = requestAnimationFrame(animate)
  
  controls.update()
  renderer.render(scene, camera)
}

// 添加一个示例立方体
const addCube = () => {
  const geometry = new THREE.BoxGeometry(1, 1, 1)
  const material = new THREE.MeshStandardMaterial({ color: 0x00ff00 })
  const cube = new THREE.Mesh(geometry, material)
  scene.add(cube)
  
  // 使用GSAP添加动画
  gsap.to(cube.rotation, {
    y: Math.PI * 2,
    duration: 2,
    repeat: -1,
    ease: 'linear'
  })
  
  return cube
}

// 暴露给父组件的方法
defineExpose({
  scene,
  camera,
  renderer,
  addCube
})

onMounted(() => {
  initThree()
  addCube()
})

onBeforeUnmount(() => {
  // 清理资源
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
  }
  
  window.removeEventListener('resize', onWindowResize)
  
  if (container.value && renderer) {
    container.value.removeChild(renderer.domElement)
  }
  
  // 释放Three.js资源
  scene.clear()
})
</script>

<template>
  <div ref="container" class="three-container">
    <!-- Three.js 渲染内容将在这里显示 -->
  </div>
</template>

<style scoped>
.three-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>