<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import ThreeScene from '../components/three/ThreeScene.vue'
import { useEconomyStore } from '../store/economyStore'
import { gsap } from 'gsap'
import * as THREE from 'three'

const router = useRouter()
const economyStore = useEconomyStore()
const threeScene = ref(null)

// 账户表单
const newAccount = ref({
  name: '',
  type: 'wallet',
  balance: 0,
  currency: 'CNY'
})

// 交易表单
const newTransaction = ref({
  accountId: '',
  amount: 0,
  type: 'income',
  category: '',
  description: '',
  date: new Date().toISOString().split('T')[0]
})

// 当前选中的账户
const selectedAccountId = ref('')

// 根据选中的账户获取交易记录
const filteredTransactions = computed(() => {
  if (!selectedAccountId.value) return []
  return economyStore.getTransactionsByAccount(selectedAccountId.value)
})

// 获取选中账户的信息
const selectedAccount = computed(() => {
  if (!selectedAccountId.value) return null
  return economyStore.getAccounts.find(account => account.id === selectedAccountId.value)
})

// 添加账户
const addAccount = () => {
  if (!newAccount.value.name) return
  
  economyStore.addAccount(newAccount.value)
  
  // 重置表单
  newAccount.value = {
    name: '',
    type: 'wallet',
    balance: 0,
    currency: 'CNY'
  }
  
  // 添加3D账户对象
  if (threeScene.value) {
    createAccountObject(economyStore.getAccounts[economyStore.getAccounts.length - 1])
  }
}

// 添加交易
const addTransaction = () => {
  if (!newTransaction.value.accountId || !newTransaction.value.category) return
  
  economyStore.addTransaction({
    ...newTransaction.value,
    date: new Date(newTransaction.value.date)
  })
  
  // 重置表单
  newTransaction.value = {
    accountId: newTransaction.value.accountId,
    amount: 0,
    type: 'income',
    category: '',
    description: '',
    date: new Date().toISOString().split('T')[0]
  }
  
  // 添加3D交易动画
  if (threeScene.value && selectedAccount.value) {
    createTransactionAnimation(selectedAccount.value)
  }
}

// 选择账户
const selectAccount = (accountId: string) => {
  selectedAccountId.value = accountId
  newTransaction.value.accountId = accountId
  
  // 高亮选中的账户对象
  highlightSelectedAccount(accountId)
}

// 创建3D经济系统场景
const createEconomyScene = () => {
  if (!threeScene.value) return
  
  const scene = threeScene.value.scene
  
  // 创建钱包/金库的底座
  const baseGeometry = new THREE.BoxGeometry(10, 0.5, 5)
  const baseMaterial = new THREE.MeshStandardMaterial({ 
    color: 0x8b4513,
    roughness: 0.7
  })
  
  const base = new THREE.Mesh(baseGeometry, baseMaterial)
  base.position.set(0, -2, 0)
  scene.add(base)
  
  // 添加场景标题
  const loader = new THREE.TextureLoader()
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')
  
  canvas.width = 512
  canvas.height = 128
  
  if (context) {
    context.fillStyle = '#4a6fa5'
    context.fillRect(0, 0, canvas.width, canvas.height)
    
    context.font = 'bold 72px Arial'
    context.fillStyle = 'white'
    context.textAlign = 'center'
    context.textBaseline = 'middle'
    context.fillText('经济系统', canvas.width / 2, canvas.height / 2)
    
    const texture = new THREE.CanvasTexture(canvas)
    const titleMaterial = new THREE.MeshBasicMaterial({ map: texture })
    const titleGeometry = new THREE.PlaneGeometry(4, 1)
    const title = new THREE.Mesh(titleGeometry, titleMaterial)
    
    title.position.set(0, 2.5, 0)
    scene.add(title)
  }
  
  // 为现有账户创建3D对象
  economyStore.getAccounts.forEach(account => {
    createAccountObject(account)
  })
}

// 创建账户3D对象
const createAccountObject = (account: any) => {
  if (!threeScene.value) return
  
  const scene = threeScene.value.scene
  
  // 根据账户类型设置不同的3D对象
  let geometry, material
  
  if (account.type === 'wallet') {
    // 钱包形状
    geometry = new THREE.BoxGeometry(1.5, 0.3, 1)
    material = new THREE.MeshStandardMaterial({ 
      color: 0x795548,
      roughness: 0.5,
      metalness: 0.2
    })
  } else if (account.type === 'bank') {
    // 银行卡形状
    geometry = new THREE.BoxGeometry(1.5, 0.1, 1)
    material = new THREE.MeshStandardMaterial({ 
      color: 0x2196f3,
      roughness: 0.2,
      metalness: 0.8
    })
  } else {
    // 其他类型
    geometry = new THREE.CylinderGeometry(0.5, 0.5, 0.5, 32)
    material = new THREE.MeshStandardMaterial({ 
      color: 0x9c27b0,
      roughness: 0.3,
      metalness: 0.5
    })
  }
  
  const accountObj = new THREE.Mesh(geometry, material)
  
  // 根据账户ID计算位置
  const accountIndex = economyStore.getAccounts.findIndex(a => a.id === account.id)
  const xPos = -3 + (accountIndex % 4) * 2
  const zPos = -1 + Math.floor(accountIndex / 4) * 2
  
  accountObj.position.set(xPos, -1.5, zPos)
  accountObj.userData = { accountId: account.id }
  
  scene.add(accountObj)
  
  // 添加账户名称
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')
  
  canvas.width = 256
  canvas.height = 64
  
  if (context) {
    context.fillStyle = 'white'
    context.font = 'bold 24px Arial'
    context.textAlign = 'center'
    context.textBaseline = 'middle'
    context.fillText(account.name, canvas.width / 2, canvas.height / 2)
    
    const texture = new THREE.CanvasTexture(canvas)
    const textMaterial = new THREE.MeshBasicMaterial({ 
      map: texture,
      transparent: true
    })
    const textGeometry = new THREE.PlaneGeometry(1.2, 0.3)
    const text = new THREE.Mesh(textGeometry, textMaterial)
    
    text.position.set(0, 0.3, 0)
    text.rotation.x = -Math.PI / 4
    accountObj.add(text)
  }
  
  // 添加余额显示
  const balanceCanvas = document.createElement('canvas')
  const balanceContext = balanceCanvas.getContext('2d')
  
  balanceCanvas.width = 256
  balanceCanvas.height = 64
  
  if (balanceContext) {
    balanceContext.fillStyle = account.balance >= 0 ? '#4caf50' : '#f44336'
    balanceContext.font = 'bold 20px Arial'
    balanceContext.textAlign = 'center'
    balanceContext.textBaseline = 'middle'
    balanceContext.fillText(
      `${account.balance.toFixed(2)} ${account.currency}`,
      balanceCanvas.width / 2,
      balanceCanvas.height / 2
    )
    
    const balanceTexture = new THREE.CanvasTexture(balanceCanvas)
    const balanceMaterial = new THREE.MeshBasicMaterial({ 
      map: balanceTexture,
      transparent: true
    })
    const balanceGeometry = new THREE.PlaneGeometry(1.2, 0.3)
    const balanceText = new THREE.Mesh(balanceGeometry, balanceMaterial)
    
    balanceText.position.set(0, 0.6, 0)
    balanceText.rotation.x = -Math.PI / 4
    accountObj.add(balanceText)
  }
  
  // 添加点击事件
  setupAccountClickHandler(accountObj)
  
  // 添加出现动画
  accountObj.scale.set(0.01, 0.01, 0.01)
  gsap.to(accountObj.scale, {
    x: 1,
    y: 1,
    z: 1,
    duration: 0.5,
    ease: 'back.out'
  })
  
  return accountObj
}

// 设置账户点击事件
const setupAccountClickHandler = (accountObj: THREE.Mesh) => {
  if (!threeScene.value) return
  
  const renderer = threeScene.value.renderer
  const camera = threeScene.value.camera
  const scene = threeScene.value.scene
  
  const raycaster = new THREE.Raycaster()
  const mouse = new THREE.Vector2()
  
  renderer.domElement.addEventListener('click', (event) => {
    // 计算鼠标在归一化设备坐标中的位置
    const rect = renderer.domElement.getBoundingClientRect()
    mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1
    
    // 更新射线投射器
    raycaster.setFromCamera(mouse, camera)
    
    // 计算与射线相交的对象
    const intersects = raycaster.intersectObjects(scene.children, true)
    
    if (intersects.length > 0) {
      let object = intersects[0].object
      
      // 查找具有accountId的父对象
      while (object && (!object.userData || !object.userData.accountId)) {
        object = object.parent as THREE.Mesh
      }
      
      if (object && object.userData && object.userData.accountId) {
        const accountId = object.userData.accountId
        selectAccount(accountId)
      }
    }
  })
}

// 高亮选中的账户
const highlightSelectedAccount = (accountId: string) => {
  if (!threeScene.value) return
  
  const scene = threeScene.value.scene
  
  // 重置所有账户的缩放
  scene.children.forEach(child => {
    if (child.userData && child.userData.accountId) {
      gsap.to(child.scale, {
        x: 1,
        y: 1,
        z: 1,
        duration: 0.3
      })
    }
  })
  
  // 高亮选中的账户
  scene.children.forEach(child => {
    if (child.userData && child.userData.accountId === accountId) {
      gsap.to(child.scale, {
        x: 1.2,
        y: 1.2,
        z: 1.2,
        duration: 0.3,
        ease: 'back.out'
      })
      
      // 移动相机聚焦到选中的账户
      const camera = threeScene.value.camera
      gsap.to(camera.position, {
        x: child.position.x,
        y: child.position.y + 3,
        z: child.position.z + 5,
        duration: 1,
        onUpdate: () => {
          camera.lookAt(child.position)
        }
      })
    }
  })
}

// 创建交易动画
const createTransactionAnimation = (account: any) => {
  if (!threeScene.value) return
  
  const scene = threeScene.value.scene
  
  // 查找账户对象
  let accountObj = null
  scene.children.forEach(child => {
    if (child.userData && child.userData.accountId === account.id) {
      accountObj = child
    }
  })
  
  if (!accountObj) return
  
  // 创建金币/钞票粒子
  const particleCount = 20
  const particles = new THREE.Group()
  
  for (let i = 0; i < particleCount; i++) {
    const isIncome = newTransaction.value.type === 'income'
    
    // 创建金币或钞票
    let particleGeometry, particleMaterial
    
    if (Math.random() > 0.5) {
      // 金币
      particleGeometry = new THREE.CylinderGeometry(0.1, 0.1, 0.02, 32)
      particleMaterial = new THREE.MeshStandardMaterial({ 
        color: 0xffd700,
        metalness: 1,
        roughness: 0.3
      })
    } else {
      // 钞票
      particleGeometry = new THREE.BoxGeometry(0.2, 0.02, 0.1)
      particleMaterial = new THREE.MeshStandardMaterial({ 
        color: isIncome ? 0x4caf50 : 0xf44336,
        metalness: 0,
        roughness: 0.5
      })
    }
    
    const particle = new THREE.Mesh(particleGeometry, particleMaterial)
    
    // 设置初始位置
    if (isIncome) {
      // 收入：从外部到账户
      particle.position.set(
        accountObj.position.x + (Math.random() - 0.5) * 5,
        accountObj.position.y + 3 + Math.random() * 2,
        accountObj.position.z + (Math.random() - 0.5) * 5
      )
    } else {
      // 支出：从账户到外部
      particle.position.set(
        accountObj.position.x,
        accountObj.position.y + 0.5,
        accountObj.position.z
      )
    }
    
    particles.add(particle)
    
    // 添加动画
    if (isIncome) {
      // 收入动画：向账户移动
      gsap.to(particle.position, {
        x: accountObj.position.x,
        y: accountObj.position.y,
        z: accountObj.position.z,
        duration: 1 + Math.random(),
        ease: 'power2.in',
        delay: i * 0.05,
        onComplete: () => {
          if (i === particleCount - 1) {
            // 最后一个粒子到达后，移除所有粒子
            setTimeout(() => {
              scene.remove(particles)
            }, 500)
          }
        }
      })
    } else {
      // 支出动画：离开账户
      gsap.to(particle.position, {
        x: accountObj.position.x + (Math.random() - 0.5) * 5,
        y: accountObj.position.y + 3 + Math.random() * 2,
        z: accountObj.position.z + (Math.random() - 0.5) * 5,
        duration: 1 + Math.random(),
        ease: 'power2.out',
        delay: i * 0.05,
        onComplete: () => {
          if (i === particleCount - 1) {
            // 最后一个粒子离开后，移除所有粒子
            setTimeout(() => {
              scene.remove(particles)
            }, 500)
          }
        }
      })
    }
    
    // 添加旋转动画
    gsap.to(particle.rotation, {
      x: Math.PI * 2 * Math.random(),
      y: Math.PI * 2 * Math.random(),
      z: Math.PI * 2 * Math.random(),
      duration: 1 + Math.random(),
      repeat: 2,
      ease: 'none'
    })
  }
  
  scene.add(particles)
}

onMounted(() => {
  if (threeScene.value) {
    // 调整相机位置
    const camera = threeScene.value.camera
    camera.position.set(0, 2, 8)
    camera.lookAt(0, -1, 0)
    
    // 创建经济系统场景
    createEconomyScene()
  }
})
</script>

<template>
  <div class="economy-view">
    <div class="three-container">
      <ThreeScene ref="threeScene" backgroundColor="#e8f4f8" />
    </div>
    
    <div class="ui-overlay">
      <div class="header">
        <h1>经济系统</h1>
        <button class="back-button" @click="router.push('/')">返回首页</button>
      </div>
      
      <div class="economy-panel">
        <div class="accounts-section">
          <div class="account-form">
            <h2>添加新账户</h2>
            <div class="form-group">
              <input 
                v-model="newAccount.name" 
                placeholder="账户名称" 
                class="form-control"
              />
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label>类型</label>
                <select v-model="newAccount.type" class="form-control">
                  <option value="wallet">钱包</option>
                  <option value="bank">银行卡</option>
                  <option value="other">其他</option>
                </select>
              </div>
              
              <div class="form-group">
                <label>初始余额</label>
                <input 
                  v-model.number="newAccount.balance" 
                  type="number" 
                  step="0.01"
                  class="form-control"
                />
              </div>
              
              <div class="form-group">
                <label>货币</label>
                <select v-model="newAccount.currency" class="form-control">
                  <option value="CNY">人民币 (CNY)</option>
                  <option value="USD">美元 (USD)</option>
                  <option value="EUR">欧元 (EUR)</option>
                </select>
              </div>
            </div>
            
            <button class="add-button" @click="addAccount">添加账户</button>
          </div>
          
          <div class="accounts-list">
            <h2>我的账户</h2>
            <div class="accounts">
              <div 
                v-for="account in economyStore.getAccounts" 
                :key="account.id"
                class="account-item"
                :class="{ 'account-selected': selectedAccountId === account.id }"
                @click="selectAccount(account.id)"
              >
                <div class="account-header">
                  <h3>{{ account.name }}</h3>
                  <span class="account-type">
                    {{ account.type === 'wallet' ? '钱包' : account.type === 'bank' ? '银行卡' : '其他' }}
                  </span>
                </div>
                
                <div class="account-balance" :class="{ 'balance-negative': account.balance < 0 }">
                  {{ account.balance.toFixed(2) }} {{ account.currency }}
                </div>
              </div>
              
              <div v-if="economyStore.getAccounts.length === 0" class="no-accounts">
                没有账户，请添加一个新账户
              </div>
            </div>
          </div>
        </div>
        
        <div class="transactions-section" v-if="selectedAccountId">
          <div class="transaction-form">
            <h2>添加新交易</h2>
            <div class="form-row">
              <div class="form-group">
                <label>类型</label>
                <select v-model="newTransaction.type" class="form-control">
                  <option value="income">收入</option>
                  <option value="expense">支出</option>
                </select>
              </div>
              
              <div class="form-group">
                <label>金额</label>
                <input 
                  v-model.number="newTransaction.amount" 
                  type="number" 
                  step="0.01"
                  min="0"
                  class="form-control"
                />
              </div>
              
              <div class="form-group">
                <label>日期</label>
                <input 
                  v-model="newTransaction.date" 
                  type="date" 
                  class="form-control"
                />
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label>分类</label>
                <input 
                  v-model="newTransaction.category" 
                  placeholder="交易分类" 
                  class="form-control"
                />
              </div>
              
              <div class="form-group">
                <label>描述</label>
                <input 
                  v-model="newTransaction.description" 
                  placeholder="交易描述" 
                  class="form-control"
                />
              </div>
            </div>
            
            <button class="add-button" @click="addTransaction">添加交易</button>
          </div>
          
          <div class="transactions-list">
            <h2>交易记录</h2>
            <div class="transactions">
              <div 
                v-for="transaction in filteredTransactions" 
                :key="transaction.id"
                class="transaction-item"
                :class="{
                  'transaction-income': transaction.type === 'income',
                  'transaction-expense': transaction.type === 'expense'
                }"
              >
                <div class="transaction-header">
                  <h3>{{ transaction.category }}</h3>
                  <span class="transaction-date">
                    {{ new Date(transaction.date).toLocaleDateString() }}
                  </span>
                </div>
                
                <p class="transaction-description">{{ transaction.description }}</p>
                
                <div class="transaction-amount">
                  {{ transaction.type === 'income' ? '+' : '-' }}
                  {{ transaction.amount.toFixed(2) }}
                  {{ selectedAccount?.currency }}
                </div>
              </div>
              
              <div v-if="filteredTransactions.length === 0" class="no-transactions">
                没有交易记录
              </div>
            </div>
          </div>
          
          <div class="account-summary">
            <h2>账户统计</h2>
            <div class="summary-cards">
              <div class="summary-card income-card">
                <h3>总收入</h3>
                <div class="summary-amount">
                  {{ economyStore.getTransactionsByAccount(selectedAccountId)
                      .filter(t => t.type === 'income')
                      .reduce((sum, t) => sum + t.amount, 0)
                      .toFixed(2) }}
                  {{ selectedAccount?.currency }}
                </div>
              </div>
              
              <div class="summary-card expense-card">
                <h3>总支出</h3>
                <div class="summary-amount">
                  {{ economyStore.getTransactionsByAccount(selectedAccountId)
                      .filter(t => t.type === 'expense')
                      .reduce((sum, t) => sum + t.amount, 0)
                      .toFixed(2) }}
                  {{ selectedAccount?.currency }}
                </div>
              </div>
              
              <div class="summary-card balance-card">
                <h3>当前余额</h3>
                <div class="summary-amount" :class="{ 'balance-negative': selectedAccount?.balance < 0 }">
                  {{ selectedAccount?.balance.toFixed(2) }}
                  {{ selectedAccount?.currency }}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="select-account-message" v-else>
          <h2>请选择一个账户</h2>
          <p>点击左侧的账户来查看交易记录和添加新交易</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.economy-view {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.three-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.ui-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  overflow-y: auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  pointer-events: auto;
}

.header h1 {
  color: #333;
  margin: 0;
}

.back-button {
  background-color: #4a6fa5;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.back-button:hover {
  background-color: #3a5a8c;
}

.economy-panel {
  display: flex;
  gap: 1rem;
  flex: 1;
  pointer-events: auto;
}

.accounts-section {
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.account-form, .accounts-list {
  background-color: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.transactions-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.transaction-form, .transactions-list, .account-summary {
  background-color: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 1rem;
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-control {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

select.form-control {
  height: 38px;
}

.add-button {
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  width: 100%;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.add-button:hover {
  background-color: #3d8b40;
}

.accounts {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-height: 300px;
  overflow-y: auto;
}

.account-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.account-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.account-selected {
  background-color: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.account-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.account-header h3 {
  margin: 0;
  font-size: 1.1rem;
}

.account-type {
  background-color: #e0e0e0;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.account-balance {
  font-size: 1.2rem;
  font-weight: bold;
  color: #4caf50;
}

.balance-negative {
  color: #f44336;
}

.no-accounts {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.transactions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-height: 300px;
  overflow-y: auto;
}

.transaction-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1rem;
  border-left: 4px solid #ccc;
}

.transaction-income {
  border-left-color: #4caf50;
}

.transaction-expense {
  border-left-color: #f44336;
}

.transaction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.transaction-header h3 {
  margin: 0;
  font-size: 1.1rem;
}

.transaction-date {
  font-size: 0.8rem;
  color: #666;
}

.transaction-description {
  color: #666;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.transaction-amount {
  font-size: 1.1rem;
  font-weight: bold;
}

.transaction-income .transaction-amount {
  color: #4caf50;
}

.transaction-expense .transaction-amount {
  color: #f44336;
}

.no-transactions {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.summary-cards {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.summary-card {
  flex: 1;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
}

.income-card {
  border-top: 4px solid #4caf50;
}

.expense-card {
  border-top: 4px solid #f44336;
}

.balance-card {
  border-top: 4px solid #2196f3;
}

.summary-card h3 {
  margin-top: 0;
  font-size: 1rem;
  color: #666;
}

.summary-amount {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
}

.select-account-message {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.select-account-message h2 {
  margin-top: 0;
  color: #333;
}

.select-account-message p {
  color: #666;
}
</style>