import { defineStore } from 'pinia'

interface Task {
  id: string
  title: string
  description: string
  type: 'daily' | 'work' | 'other'
  priority: 'low' | 'medium' | 'high'
  dueDate?: Date
  status: 'todo' | 'inProgress' | 'completed'
  experience: number
  createdAt: Date
}

export const useTaskStore = defineStore('task', {
  state: () => ({
    tasks: [] as Task[]
  }),
  getters: {
    getTasks: (state) => state.tasks,
    getTasksByType: (state) => (type: Task['type']) => {
      return state.tasks.filter(task => task.type === type)
    },
    getTasksByStatus: (state) => (status: Task['status']) => {
      return state.tasks.filter(task => task.status === status)
    },
    getTasksByPriority: (state) => (priority: Task['priority']) => {
      return state.tasks.filter(task => task.priority === priority)
    }
  },
  actions: {
    addTask(task: Omit<Task, 'id' | 'createdAt'>) {
      const newTask: Task = {
        ...task,
        id: Date.now().toString(),
        createdAt: new Date()
      }
      this.tasks.push(newTask)
    },
    updateTask(id: string, updates: Partial<Task>) {
      const index = this.tasks.findIndex(task => task.id === id)
      if (index !== -1) {
        this.tasks[index] = { ...this.tasks[index], ...updates }
      }
    },
    deleteTask(id: string) {
      this.tasks = this.tasks.filter(task => task.id !== id)
    },
    completeTask(id: string) {
      const index = this.tasks.findIndex(task => task.id === id)
      if (index !== -1) {
        this.tasks[index].status = 'completed'
        // 这里可以添加经验值增加的逻辑
        return this.tasks[index].experience
      }
      return 0
    }
  }
})