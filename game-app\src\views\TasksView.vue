<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import ThreeScene from '../components/three/ThreeScene.vue'
import { useTaskStore } from '../store/taskStore'
import { useHonorStore } from '../store/honorStore'
import { gsap } from 'gsap'
import * as THREE from 'three'

const router = useRouter()
const taskStore = useTaskStore()
const honorStore = useHonorStore()
const threeScene = ref(null)

// 任务表单
const newTask = ref({
  title: '',
  description: '',
  type: 'daily',
  priority: 'medium',
  experience: 10
})

// 筛选选项
const filter = ref({
  type: 'all',
  status: 'all',
  priority: 'all'
})

// 根据筛选条件获取任务
const filteredTasks = computed(() => {
  let tasks = taskStore.getTasks
  
  if (filter.value.type !== 'all') {
    tasks = tasks.filter(task => task.type === filter.value.type)
  }
  
  if (filter.value.status !== 'all') {
    tasks = tasks.filter(task => task.status === filter.value.status)
  }
  
  if (filter.value.priority !== 'all') {
    tasks = tasks.filter(task => task.priority === filter.value.priority)
  }
  
  return tasks
})

// 添加任务
const addTask = () => {
  if (!newTask.value.title) return
  
  taskStore.addTask({
    ...newTask.value,
    status: 'todo'
  })
  
  // 重置表单
  newTask.value = {
    title: '',
    description: '',
    type: 'daily',
    priority: 'medium',
    experience: 10
  }
  
  // 添加3D任务卡片
  if (threeScene.value) {
    createTaskCard(taskStore.getTasks[taskStore.getTasks.length - 1])
  }
}

// 完成任务
const completeTask = (taskId: string) => {
  const experience = taskStore.completeTask(taskId)
  
  // 添加经验值
  if (experience > 0) {
    honorStore.addExperience(experience)
    
    // 显示经验值获取动画
    showExperienceGain(experience)
  }
  
  // 更新3D任务卡片
  updateTaskCard(taskId)
}

// 显示经验值获取动画
const showExperienceGain = (amount: number) => {
  const expElement = document.createElement('div')
  expElement.className = 'experience-gain'
  expElement.textContent = `+${amount} 经验`
  document.body.appendChild(expElement)
  
  gsap.fromTo(expElement, 
    { opacity: 0, y: 0 },
    { opacity: 1, y: -50, duration: 1, onComplete: () => {
      gsap.to(expElement, { opacity: 0, duration: 0.5, onComplete: () => {
        document.body.removeChild(expElement)
      }})
    }}
  )
}

// 创建3D任务板
const createTaskBoard = () => {
  if (!threeScene.value) return
  
  const scene = threeScene.value.scene
  
  // 创建任务板
  const boardGeometry = new THREE.BoxGeometry(8, 6, 0.2)
  const boardMaterial = new THREE.MeshStandardMaterial({ 
    color: 0xf5f5f5,
    roughness: 0.2
  })
  
  const board = new THREE.Mesh(boardGeometry, boardMaterial)
  board.position.set(0, 0, -1)
  scene.add(board)
  
  // 添加任务板标题
  const loader = new THREE.TextureLoader()
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')
  
  canvas.width = 512
  canvas.height = 128
  
  if (context) {
    context.fillStyle = '#4a6fa5'
    context.fillRect(0, 0, canvas.width, canvas.height)
    
    context.font = 'bold 72px Arial'
    context.fillStyle = 'white'
    context.textAlign = 'center'
    context.textBaseline = 'middle'
    context.fillText('任务系统', canvas.width / 2, canvas.height / 2)
    
    const texture = new THREE.CanvasTexture(canvas)
    const titleMaterial = new THREE.MeshBasicMaterial({ map: texture })
    const titleGeometry = new THREE.PlaneGeometry(4, 1)
    const title = new THREE.Mesh(titleGeometry, titleMaterial)
    
    title.position.set(0, 2.5, -0.8)
    scene.add(title)
  }
  
  // 为现有任务创建卡片
  taskStore.getTasks.forEach(task => {
    createTaskCard(task)
  })
}

// 创建任务卡片
const createTaskCard = (task: any) => {
  if (!threeScene.value) return
  
  const scene = threeScene.value.scene
  
  // 根据任务优先级设置颜色
  const priorityColors = {
    low: 0x4caf50,
    medium: 0xff9800,
    high: 0xf44336
  }
  
  const cardGeometry = new THREE.BoxGeometry(2, 0.8, 0.1)
  const cardMaterial = new THREE.MeshStandardMaterial({ 
    color: priorityColors[task.priority],
    transparent: true,
    opacity: task.status === 'completed' ? 0.5 : 1
  })
  
  const card = new THREE.Mesh(cardGeometry, cardMaterial)
  
  // 根据任务ID计算位置
  const taskIndex = taskStore.getTasks.findIndex(t => t.id === task.id)
  const row = Math.floor(taskIndex / 3)
  const col = taskIndex % 3
  
  card.position.set(-2.5 + col * 2.5, 1 - row * 1, -0.8)
  card.userData = { taskId: task.id }
  
  scene.add(card)
  
  // 添加任务标题
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')
  
  canvas.width = 256
  canvas.height = 128
  
  if (context) {
    context.fillStyle = 'white'
    context.font = 'bold 24px Arial'
    context.textAlign = 'center'
    context.textBaseline = 'middle'
    context.fillText(task.title, canvas.width / 2, canvas.height / 2)
    
    const texture = new THREE.CanvasTexture(canvas)
    const textMaterial = new THREE.MeshBasicMaterial({ 
      map: texture,
      transparent: true
    })
    const textGeometry = new THREE.PlaneGeometry(1.8, 0.6)
    const text = new THREE.Mesh(textGeometry, textMaterial)
    
    text.position.set(0, 0, 0.06)
    card.add(text)
  }
  
  // 添加点击事件
  return card
}

// 更新任务卡片
const updateTaskCard = (taskId: string) => {
  if (!threeScene.value) return
  
  const scene = threeScene.value.scene
  const task = taskStore.getTasks.find(t => t.id === taskId)
  
  if (!task) return
  
  // 查找对应的任务卡片
  scene.children.forEach(child => {
    if (child.userData && child.userData.taskId === taskId) {
      // 如果任务已完成，添加完成动画
      if (task.status === 'completed') {
        gsap.to(child.position, {
          z: -5,
          y: child.position.y + 2,
          duration: 1,
          ease: 'back.in',
          onComplete: () => {
            scene.remove(child)
          }
        })
        
        gsap.to(child.rotation, {
          z: Math.PI * 2,
          duration: 1
        })
        
        // 添加粒子效果
        createCompletionParticles(child.position)
      }
    }
  })
}

// 创建任务完成粒子效果
const createCompletionParticles = (position: THREE.Vector3) => {
  if (!threeScene.value) return
  
  const scene = threeScene.value.scene
  
  // 创建粒子几何体
  const particlesGeometry = new THREE.BufferGeometry()
  const particleCount = 50
  
  const positions = new Float32Array(particleCount * 3)
  const colors = new Float32Array(particleCount * 3)
  
  for (let i = 0; i < particleCount; i++) {
    // 初始位置在任务卡片位置
    positions[i * 3] = position.x
    positions[i * 3 + 1] = position.y
    positions[i * 3 + 2] = position.z
    
    // 随机颜色
    colors[i * 3] = Math.random()
    colors[i * 3 + 1] = Math.random()
    colors[i * 3 + 2] = Math.random()
  }
  
  particlesGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
  particlesGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3))
  
  // 创建粒子材质
  const particlesMaterial = new THREE.PointsMaterial({
    size: 0.1,
    vertexColors: true,
    transparent: true
  })
  
  // 创建粒子系统
  const particles = new THREE.Points(particlesGeometry, particlesMaterial)
  scene.add(particles)
  
  // 为每个粒子创建动画
  for (let i = 0; i < particleCount; i++) {
    const i3 = i * 3
    
    // 随机方向和距离
    const targetX = position.x + (Math.random() - 0.5) * 5
    const targetY = position.y + (Math.random() - 0.5) * 5
    const targetZ = position.z + (Math.random() - 0.5) * 5
    
    gsap.to(particlesGeometry.attributes.position.array, {
      [i3]: targetX,
      [i3 + 1]: targetY,
      [i3 + 2]: targetZ,
      duration: 1 + Math.random(),
      ease: 'power2.out',
      onUpdate: () => {
        particlesGeometry.attributes.position.needsUpdate = true
      },
      onComplete: () => {
        if (i === particleCount - 1) {
          // 最后一个粒子动画完成后，移除粒子系统
          setTimeout(() => {
            scene.remove(particles)
          }, 1000)
        }
      }
    })
    
    // 淡出效果
    gsap.to(particlesMaterial, {
      opacity: 0,
      duration: 2,
      delay: 0.5
    })
  }
}

onMounted(() => {
  if (threeScene.value) {
    // 调整相机位置
    const camera = threeScene.value.camera
    camera.position.set(0, 0, 5)
    camera.lookAt(0, 0, 0)
    
    // 创建任务板
    createTaskBoard()
  }
})
</script>

<template>
  <div class="tasks-view">
    <div class="three-container">
      <ThreeScene ref="threeScene" backgroundColor="#e8f4f8" />
    </div>
    
    <div class="ui-overlay">
      <div class="header">
        <h1>任务系统</h1>
        <button class="back-button" @click="router.push('/')">返回首页</button>
      </div>
      
      <div class="task-panel">
        <div class="task-form">
          <h2>添加新任务</h2>
          <div class="form-group">
            <input 
              v-model="newTask.title" 
              placeholder="任务标题" 
              class="form-control"
            />
          </div>
          
          <div class="form-group">
            <textarea 
              v-model="newTask.description" 
              placeholder="任务描述" 
              class="form-control"
            ></textarea>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label>类型</label>
              <select v-model="newTask.type" class="form-control">
                <option value="daily">日常任务</option>
                <option value="work">工作任务</option>
                <option value="other">其他任务</option>
              </select>
            </div>
            
            <div class="form-group">
              <label>优先级</label>
              <select v-model="newTask.priority" class="form-control">
                <option value="low">低</option>
                <option value="medium">中</option>
                <option value="high">高</option>
              </select>
            </div>
            
            <div class="form-group">
              <label>经验值</label>
              <input 
                v-model.number="newTask.experience" 
                type="number" 
                min="1" 
                class="form-control"
              />
            </div>
          </div>
          
          <button class="add-button" @click="addTask">添加任务</button>
        </div>
        
        <div class="task-list">
          <div class="filter-controls">
            <div class="filter-group">
              <label>类型</label>
              <select v-model="filter.type">
                <option value="all">全部</option>
                <option value="daily">日常任务</option>
                <option value="work">工作任务</option>
                <option value="other">其他任务</option>
              </select>
            </div>
            
            <div class="filter-group">
              <label>状态</label>
              <select v-model="filter.status">
                <option value="all">全部</option>
                <option value="todo">待完成</option>
                <option value="inProgress">进行中</option>
                <option value="completed">已完成</option>
              </select>
            </div>
            
            <div class="filter-group">
              <label>优先级</label>
              <select v-model="filter.priority">
                <option value="all">全部</option>
                <option value="low">低</option>
                <option value="medium">中</option>
                <option value="high">高</option>
              </select>
            </div>
          </div>
          
          <div class="tasks">
            <div 
              v-for="task in filteredTasks" 
              :key="task.id"
              class="task-item"
              :class="{
                'task-completed': task.status === 'completed',
                'task-high': task.priority === 'high',
                'task-medium': task.priority === 'medium',
                'task-low': task.priority === 'low'
              }"
            >
              <div class="task-header">
                <h3>{{ task.title }}</h3>
                <span class="task-type">{{ task.type === 'daily' ? '日常' : task.type === 'work' ? '工作' : '其他' }}</span>
              </div>
              
              <p class="task-description">{{ task.description }}</p>
              
              <div class="task-footer">
                <span class="task-exp">经验值: {{ task.experience }}</span>
                
                <button 
                  v-if="task.status !== 'completed'"
                  class="complete-button"
                  @click="completeTask(task.id)"
                >
                  完成任务
                </button>
                
                <span v-else class="completed-label">已完成</span>
              </div>
            </div>
            
            <div v-if="filteredTasks.length === 0" class="no-tasks">
              没有符合条件的任务
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.tasks-view {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.three-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.ui-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  overflow-y: auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  pointer-events: auto;
}

.header h1 {
  color: #333;
  margin: 0;
}

.back-button {
  background-color: #4a6fa5;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.back-button:hover {
  background-color: #3a5a8c;
}

.task-panel {
  display: flex;
  gap: 1rem;
  flex: 1;
  pointer-events: auto;
}

.task-form {
  background-color: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 300px;
  height: fit-content;
}

.task-list {
  background-color: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  flex: 1;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 1rem;
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-control {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

select.form-control {
  height: 38px;
}

textarea.form-control {
  min-height: 80px;
  resize: vertical;
}

.add-button {
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  width: 100%;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.add-button:hover {
  background-color: #3d8b40;
}

.filter-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.filter-group select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.tasks {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.task-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
  border-left: 5px solid #ccc;
}

.task-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.task-high {
  border-left-color: #f44336;
}

.task-medium {
  border-left-color: #ff9800;
}

.task-low {
  border-left-color: #4caf50;
}

.task-completed {
  opacity: 0.6;
  border-left-color: #9e9e9e;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.task-header h3 {
  margin: 0;
  font-size: 1.1rem;
}

.task-type {
  background-color: #e0e0e0;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.task-description {
  color: #666;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-exp {
  font-size: 0.9rem;
  color: #666;
}

.complete-button {
  background-color: #4a6fa5;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.complete-button:hover {
  background-color: #3a5a8c;
}

.completed-label {
  color: #4caf50;
  font-weight: bold;
  font-size: 0.9rem;
}

.no-tasks {
  text-align: center;
  padding: 2rem;
  color: #666;
  grid-column: 1 / -1;
}

.experience-gain {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(76, 175, 80, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: bold;
  pointer-events: none;
  z-index: 1000;
}
</style>