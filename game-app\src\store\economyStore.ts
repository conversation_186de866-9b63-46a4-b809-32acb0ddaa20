import { defineStore } from 'pinia'

interface Account {
  id: string
  name: string
  type: 'wallet' | 'bank' | 'other'
  balance: number
  currency: string
  createdAt: Date
}

interface Transaction {
  id: string
  accountId: string
  amount: number
  type: 'income' | 'expense'
  category: string
  description: string
  date: Date
  createdAt: Date
}

export const useEconomyStore = defineStore('economy', {
  state: () => ({
    accounts: [] as Account[],
    transactions: [] as Transaction[]
  }),
  getters: {
    getAccounts: (state) => state.accounts,
    getTransactions: (state) => state.transactions,
    getTotalBalance: (state) => {
      return state.accounts.reduce((total, account) => total + account.balance, 0)
    },
    getTransactionsByAccount: (state) => (accountId: string) => {
      return state.transactions.filter(transaction => transaction.accountId === accountId)
    },
    getTransactionsByType: (state) => (type: Transaction['type']) => {
      return state.transactions.filter(transaction => transaction.type === type)
    },
    getTransactionsByCategory: (state) => (category: string) => {
      return state.transactions.filter(transaction => transaction.category === category)
    },
    getIncomeTotal: (state) => {
      return state.transactions
        .filter(transaction => transaction.type === 'income')
        .reduce((total, transaction) => total + transaction.amount, 0)
    },
    getExpenseTotal: (state) => {
      return state.transactions
        .filter(transaction => transaction.type === 'expense')
        .reduce((total, transaction) => total + transaction.amount, 0)
    }
  },
  actions: {
    addAccount(account: Omit<Account, 'id' | 'createdAt'>) {
      const newAccount: Account = {
        ...account,
        id: Date.now().toString(),
        createdAt: new Date()
      }
      this.accounts.push(newAccount)
    },
    updateAccount(id: string, updates: Partial<Account>) {
      const index = this.accounts.findIndex(account => account.id === id)
      if (index !== -1) {
        this.accounts[index] = { ...this.accounts[index], ...updates }
      }
    },
    deleteAccount(id: string) {
      this.accounts = this.accounts.filter(account => account.id !== id)
      // 删除账户时，同时删除该账户的所有交易记录
      this.transactions = this.transactions.filter(transaction => transaction.accountId !== id)
    },
    addTransaction(transaction: Omit<Transaction, 'id' | 'createdAt'>) {
      const newTransaction: Transaction = {
        ...transaction,
        id: Date.now().toString(),
        createdAt: new Date()
      }
      this.transactions.push(newTransaction)
      
      // 更新账户余额
      const accountIndex = this.accounts.findIndex(account => account.id === transaction.accountId)
      if (accountIndex !== -1) {
        const amount = transaction.type === 'income' ? transaction.amount : -transaction.amount
        this.accounts[accountIndex].balance += amount
      }
    },
    updateTransaction(id: string, updates: Partial<Transaction>) {
      const index = this.transactions.findIndex(transaction => transaction.id === id)
      if (index !== -1) {
        // 如果金额或类型发生变化，需要更新账户余额
        const oldTransaction = this.transactions[index]
        const oldAmount = oldTransaction.type === 'income' ? oldTransaction.amount : -oldTransaction.amount
        
        this.transactions[index] = { ...oldTransaction, ...updates }
        
        if (updates.amount !== undefined || updates.type !== undefined) {
          const newTransaction = this.transactions[index]
          const newAmount = newTransaction.type === 'income' ? newTransaction.amount : -newTransaction.amount
          
          const accountIndex = this.accounts.findIndex(account => account.id === oldTransaction.accountId)
          if (accountIndex !== -1) {
            this.accounts[accountIndex].balance = this.accounts[accountIndex].balance - oldAmount + newAmount
          }
        }
      }
    },
    deleteTransaction(id: string) {
      const transaction = this.transactions.find(t => t.id === id)
      if (transaction) {
        // 更新账户余额
        const accountIndex = this.accounts.findIndex(account => account.id === transaction.accountId)
        if (accountIndex !== -1) {
          const amount = transaction.type === 'income' ? -transaction.amount : transaction.amount
          this.accounts[accountIndex].balance += amount
        }
        
        this.transactions = this.transactions.filter(t => t.id !== id)
      }
    }
  }
})