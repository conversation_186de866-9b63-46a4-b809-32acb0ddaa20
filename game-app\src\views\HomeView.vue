<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import ThreeScene from '../components/three/ThreeScene.vue'
import { gsap } from 'gsap'
import * as THREE from 'three'

const router = useRouter()
const threeScene = ref(null)

// 模块配置
const modules = [
  { name: '任务', path: '/tasks', color: '#FF6B6B', position: { x: -2, y: 0, z: 0 } },
  { name: '经济', path: '/economy', color: '#4ECDC4', position: { x: 0, y: 0, z: 0 } },
  { name: '荣誉', path: '/honors', color: '#FFD166', position: { x: 2, y: 0, z: 0 } }
]

// 创建模块的3D对象
const createModules = () => {
  if (!threeScene.value) return
  
  const scene = threeScene.value.scene
  
  modules.forEach((module, index) => {
    // 创建模块的几何体
    const geometry = new THREE.BoxGeometry(1.5, 1.5, 1.5)
    const material = new THREE.MeshStandardMaterial({ 
      color: module.color,
      roughness: 0.3,
      metalness: 0.5
    })
    
    const moduleMesh = new THREE.Mesh(geometry, material)
    moduleMesh.position.set(module.position.x, module.position.y, module.position.z)
    moduleMesh.userData = { moduleIndex: index } // 存储模块索引用于点击检测
    
    scene.add(moduleMesh)
    
    // 添加悬浮动画
    gsap.to(moduleMesh.position, {
      y: '+= 0.2',
      duration: 1.5,
      repeat: -1,
      yoyo: true,
      ease: 'sine.inOut'
    })
  })
  
  // 添加点击事件
  setupClickHandler()
}

// 设置点击事件处理
const setupClickHandler = () => {
  if (!threeScene.value) return
  
  const renderer = threeScene.value.renderer
  const camera = threeScene.value.camera
  const scene = threeScene.value.scene
  
  const raycaster = new THREE.Raycaster()
  const mouse = new THREE.Vector2()
  
  renderer.domElement.addEventListener('click', (event) => {
    // 计算鼠标在归一化设备坐标中的位置
    const rect = renderer.domElement.getBoundingClientRect()
    mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1
    
    // 更新射线投射器
    raycaster.setFromCamera(mouse, camera)
    
    // 计算与射线相交的对象
    const intersects = raycaster.intersectObjects(scene.children)
    
    if (intersects.length > 0) {
      const object = intersects[0].object
      
      // 检查是否点击了模块
      if (object.userData && object.userData.moduleIndex !== undefined) {
        const moduleIndex = object.userData.moduleIndex
        const module = modules[moduleIndex]
        
        // 添加点击动画
        gsap.to(object.scale, {
          x: 1.2,
          y: 1.2,
          z: 1.2,
          duration: 0.2,
          yoyo: true,
          repeat: 1,
          onComplete: () => {
            // 动画完成后导航到对应页面
            router.push(module.path)
          }
        })
      }
    }
  })
}

// 创建角色
const createCharacter = () => {
  if (!threeScene.value) return
  
  const scene = threeScene.value.scene
  
  // 创建简单的角色（后期可以替换为更复杂的模型）
  const head = new THREE.Mesh(
    new THREE.SphereGeometry(0.5, 32, 32),
    new THREE.MeshStandardMaterial({ color: 0xffcc99 })
  )
  head.position.set(0, 3, -3)
  
  const body = new THREE.Mesh(
    new THREE.CylinderGeometry(0.3, 0.5, 1, 32),
    new THREE.MeshStandardMaterial({ color: 0x3366ff })
  )
  body.position.set(0, 2, -3)
  
  scene.add(head)
  scene.add(body)
  
  // 添加角色动画
  gsap.to(head.position, {
    y: '+= 0.1',
    duration: 1,
    repeat: -1,
    yoyo: true,
    ease: 'sine.inOut'
  })
}

onMounted(() => {
  if (threeScene.value) {
    // 调整相机位置
    const camera = threeScene.value.camera
    camera.position.set(0, 2, 8)
    camera.lookAt(0, 0, 0)
    
    // 创建模块和角色
    createModules()
    createCharacter()
  }
})
</script>

<template>
  <div class="home-view">
    <div class="three-container">
      <ThreeScene ref="threeScene" backgroundColor="#87CEEB" />
    </div>
    
    <div class="ui-overlay">
      <h1 class="game-title">生活游戏化</h1>
      
      <div class="module-buttons">
        <router-link 
          v-for="(module, index) in modules" 
          :key="index" 
          :to="module.path"
          class="module-button"
          :style="{ backgroundColor: module.color }"
        >
          {{ module.name }}
        </router-link>
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-view {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.three-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.ui-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 2rem;
}

.game-title {
  font-size: 2.5rem;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  text-align: center;
  margin-top: 2rem;
}

.module-buttons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  pointer-events: auto;
}

.module-button {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  color: white;
  font-weight: bold;
  text-decoration: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.module-button:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}
</style>