<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import ThreeScene from '../components/three/ThreeScene.vue'
import { useHonorStore } from '../store/honorStore'
import { gsap } from 'gsap'
import * as THREE from 'three'

const router = useRouter()
const honorStore = useHonorStore()
const threeScene = ref(null)

// 当前选中的卡片
const selectedCardId = ref('')

// 卡片翻转状态
const flippedCards = ref<Record<string, boolean>>({})

// 获取选中的卡片信息
const selectedCard = computed(() => {
  if (!selectedCardId.value) return null
  return honorStore.getCards.find(card => card.id === selectedCardId.value)
})

// 卡片分类
const cardCategories = computed(() => {
  const categories = new Set<string>()
  honorStore.getCards.forEach(card => {
    categories.add(card.category)
  })
  return Array.from(categories)
})

// 按分类获取卡片
const getCardsByCategory = (category: string) => {
  return honorStore.getCards.filter(card => card.category === category)
}

// 选择卡片
const selectCard = (cardId: string) => {
  selectedCardId.value = cardId
  
  // 高亮选中的卡片
  highlightSelectedCard(cardId)
}

// 翻转卡片
const flipCard = (cardId: string) => {
  flippedCards.value[cardId] = !flippedCards.value[cardId]
  
  // 更新3D卡片翻转
  updateCardFlip(cardId, flippedCards.value[cardId])
}

// 创建3D荣誉系统场景
const createHonorsScene = () => {
  if (!threeScene.value) return
  
  const scene = threeScene.value.scene
  
  // 创建卡片墙的底座
  const baseGeometry = new THREE.BoxGeometry(12, 0.5, 6)
  const baseMaterial = new THREE.MeshStandardMaterial({ 
    color: 0x2c3e50,
    roughness: 0.7
  })
  
  const base = new THREE.Mesh(baseGeometry, baseMaterial)
  base.position.set(0, -2, 0)
  scene.add(base)
  
  // 添加场景标题
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')
  
  canvas.width = 512
  canvas.height = 128
  
  if (context) {
    context.fillStyle = '#8e44ad'
    context.fillRect(0, 0, canvas.width, canvas.height)
    
    context.font = 'bold 72px Arial'
    context.fillStyle = 'white'
    context.textAlign = 'center'
    context.textBaseline = 'middle'
    context.fillText('荣誉系统', canvas.width / 2, canvas.height / 2)
    
    const texture = new THREE.CanvasTexture(canvas)
    const titleMaterial = new THREE.MeshBasicMaterial({ map: texture })
    const titleGeometry = new THREE.PlaneGeometry(4, 1)
    const title = new THREE.Mesh(titleGeometry, titleMaterial)
    
    title.position.set(0, 2.5, 0)
    scene.add(title)
  }
  
  // 为现有卡片创建3D对象
  const cardsByCategory = {}
  cardCategories.value.forEach(category => {
    cardsByCategory[category] = getCardsByCategory(category)
  })
  
  // 按分类创建卡片墙
  let categoryIndex = 0
  for (const category in cardsByCategory) {
    const cards = cardsByCategory[category]
    const categoryGroup = new THREE.Group()
    
    // 创建分类标题
    const categoryCanvas = document.createElement('canvas')
    const categoryContext = categoryCanvas.getContext('2d')
    
    categoryCanvas.width = 256
    categoryCanvas.height = 64
    
    if (categoryContext) {
      categoryContext.fillStyle = '#34495e'
      categoryContext.fillRect(0, 0, categoryCanvas.width, categoryCanvas.height)
      
      categoryContext.font = 'bold 32px Arial'
      categoryContext.fillStyle = 'white'
      categoryContext.textAlign = 'center'
      categoryContext.textBaseline = 'middle'
      categoryContext.fillText(category, categoryCanvas.width / 2, categoryCanvas.height / 2)
      
      const categoryTexture = new THREE.CanvasTexture(categoryCanvas)
      const categoryMaterial = new THREE.MeshBasicMaterial({ map: categoryTexture })
      const categoryGeometry = new THREE.PlaneGeometry(2, 0.5)
      const categoryTitle = new THREE.Mesh(categoryGeometry, categoryMaterial)
      
      categoryTitle.position.set(0, 1.5, 0)
      categoryGroup.add(categoryTitle)
    }
    
    // 创建卡片
    cards.forEach((card, index) => {
      const cardObj = createCardObject(card, index)
      categoryGroup.add(cardObj)
    })
    
    // 定位分类组
    const xOffset = (categoryIndex - Math.floor(cardCategories.value.length / 2)) * 6
    categoryGroup.position.set(xOffset, 0, 0)
    scene.add(categoryGroup)
    
    categoryIndex++
  }
}

// 创建卡片3D对象
const createCardObject = (card: any, index: number) => {
  // 创建卡片组
  const cardGroup = new THREE.Group()
  cardGroup.userData = { cardId: card.id }
  
  // 卡片正面
  const frontGeometry = new THREE.PlaneGeometry(1.5, 2)
  const frontMaterial = new THREE.MeshStandardMaterial({ 
    color: card.unlocked ? getRarityColor(card.rarity) : 0x7f8c8d,
    roughness: 0.3,
    metalness: 0.7,
    side: THREE.DoubleSide
  })
  
  const frontCard = new THREE.Mesh(frontGeometry, frontMaterial)
  frontCard.position.set(0, 0, 0.01)
  cardGroup.add(frontCard)
  
  // 卡片背面
  const backGeometry = new THREE.PlaneGeometry(1.5, 2)
  const backMaterial = new THREE.MeshStandardMaterial({ 
    color: 0x2c3e50,
    roughness: 0.5,
    metalness: 0.5,
    side: THREE.DoubleSide
  })
  
  const backCard = new THREE.Mesh(backGeometry, backMaterial)
  backCard.position.set(0, 0, -0.01)
  backCard.rotation.y = Math.PI
  cardGroup.add(backCard)
  
  // 添加卡片标题
  const titleCanvas = document.createElement('canvas')
  const titleContext = titleCanvas.getContext('2d')
  
  titleCanvas.width = 256
  titleCanvas.height = 64
  
  if (titleContext) {
    titleContext.fillStyle = 'rgba(0, 0, 0, 0.7)'
    titleContext.fillRect(0, 0, titleCanvas.width, titleCanvas.height)
    
    titleContext.font = 'bold 24px Arial'
    titleContext.fillStyle = 'white'
    titleContext.textAlign = 'center'
    titleContext.textBaseline = 'middle'
    titleContext.fillText(
      card.unlocked ? card.name : '???', 
      titleCanvas.width / 2, 
      titleCanvas.height / 2
    )
    
    const titleTexture = new THREE.CanvasTexture(titleCanvas)
    const titleMaterial = new THREE.MeshBasicMaterial({ 
      map: titleTexture,
      transparent: true
    })
    const titleGeometry = new THREE.PlaneGeometry(1.4, 0.35)
    const title = new THREE.Mesh(titleGeometry, titleMaterial)
    
    title.position.set(0, 0.8, 0.02)
    frontCard.add(title)
  }
  
  // 添加卡片描述
  if (card.unlocked) {
    const descCanvas = document.createElement('canvas')
    const descContext = descCanvas.getContext('2d')
    
    descCanvas.width = 256
    descCanvas.height = 128
    
    if (descContext) {
      descContext.fillStyle = 'rgba(0, 0, 0, 0.5)'
      descContext.fillRect(0, 0, descCanvas.width, descCanvas.height)
      
      descContext.font = '16px Arial'
      descContext.fillStyle = 'white'
      descContext.textAlign = 'center'
      descContext.textBaseline = 'middle'
      
      // 文本换行处理
      const maxWidth = 230
      const words = card.description.split(' ')
      let line = ''
      let lines = []
      let y = 30
      
      for (let i = 0; i < words.length; i++) {
        const testLine = line + words[i] + ' '
        const metrics = descContext.measureText(testLine)
        const testWidth = metrics.width
        
        if (testWidth > maxWidth && i > 0) {
          lines.push(line)
          line = words[i] + ' '
        } else {
          line = testLine
        }
      }
      lines.push(line)
      
      // 绘制文本
      lines.forEach((line, i) => {
        descContext.fillText(line, descCanvas.width / 2, y + i * 20)
      })
      
      const descTexture = new THREE.CanvasTexture(descCanvas)
      const descMaterial = new THREE.MeshBasicMaterial({ 
        map: descTexture,
        transparent: true
      })
      const descGeometry = new THREE.PlaneGeometry(1.4, 0.7)
      const desc = new THREE.Mesh(descGeometry, descMaterial)
      
      desc.position.set(0, 0.2, 0.02)
      frontCard.add(desc)
    }
  }
  
  // 添加稀有度标识
  if (card.unlocked) {
    const rarityCanvas = document.createElement('canvas')
    const rarityContext = rarityCanvas.getContext('2d')
    
    rarityCanvas.width = 128
    rarityCanvas.height = 32
    
    if (rarityContext) {
      rarityContext.fillStyle = 'rgba(0, 0, 0, 0.7)'
      rarityContext.fillRect(0, 0, rarityCanvas.width, rarityCanvas.height)
      
      rarityContext.font = 'bold 16px Arial'
      rarityContext.fillStyle = getRarityColorHex(card.rarity)
      rarityContext.textAlign = 'center'
      rarityContext.textBaseline = 'middle'
      rarityContext.fillText(
        getRarityStars(card.rarity), 
        rarityCanvas.width / 2, 
        rarityCanvas.height / 2
      )
      
      const rarityTexture = new THREE.CanvasTexture(rarityCanvas)
      const rarityMaterial = new THREE.MeshBasicMaterial({ 
        map: rarityTexture,
        transparent: true
      })
      const rarityGeometry = new THREE.PlaneGeometry(0.7, 0.2)
      const rarity = new THREE.Mesh(rarityGeometry, rarityMaterial)
      
      rarity.position.set(0, -0.8, 0.02)
      frontCard.add(rarity)
    }
  }
  
  // 添加卡片背面内容
  const backContentCanvas = document.createElement('canvas')
  const backContentContext = backContentCanvas.getContext('2d')
  
  backContentCanvas.width = 256
  backContentCanvas.height = 256
  
  if (backContentContext) {
    backContentContext.fillStyle = '#34495e'
    backContentContext.fillRect(0, 0, backContentCanvas.width, backContentCanvas.height)
    
    // 绘制花纹
    backContentContext.strokeStyle = '#2c3e50'
    backContentContext.lineWidth = 5
    
    // 绘制边框
    backContentContext.strokeRect(20, 20, backContentCanvas.width - 40, backContentCanvas.height - 40)
    
    // 绘制对角线
    backContentContext.beginPath()
    backContentContext.moveTo(20, 20)
    backContentContext.lineTo(backContentCanvas.width - 20, backContentCanvas.height - 20)
    backContentContext.stroke()
    
    backContentContext.beginPath()
    backContentContext.moveTo(backContentCanvas.width - 20, 20)
    backContentContext.lineTo(20, backContentCanvas.height - 20)
    backContentContext.stroke()
    
    // 绘制中心圆
    backContentContext.beginPath()
    backContentContext.arc(backContentCanvas.width / 2, backContentCanvas.height / 2, 50, 0, Math.PI * 2)
    backContentContext.stroke()
    
    // 绘制文字
    backContentContext.font = 'bold 24px Arial'
    backContentContext.fillStyle = '#ecf0f1'
    backContentContext.textAlign = 'center'
    backContentContext.textBaseline = 'middle'
    backContentContext.fillText('荣誉卡片', backContentCanvas.width / 2, backContentCanvas.height / 2)
    
    const backContentTexture = new THREE.CanvasTexture(backContentCanvas)
    const backContentMaterial = new THREE.MeshBasicMaterial({ 
      map: backContentTexture,
      transparent: true
    })
    const backContentGeometry = new THREE.PlaneGeometry(1.4, 1.9)
    const backContent = new THREE.Mesh(backContentGeometry, backContentMaterial)
    
    backContent.position.set(0, 0, 0.02)
    backCard.add(backContent)
  }
  
  // 如果卡片未解锁，添加锁图标
  if (!card.unlocked) {
    const lockCanvas = document.createElement('canvas')
    const lockContext = lockCanvas.getContext('2d')
    
    lockCanvas.width = 128
    lockCanvas.height = 128
    
    if (lockContext) {
      // 绘制锁图标
      lockContext.fillStyle = 'rgba(0, 0, 0, 0.7)'
      lockContext.beginPath()
      lockContext.arc(64, 48, 32, 0, Math.PI * 2)
      lockContext.fill()
      
      lockContext.fillRect(32, 48, 64, 64)
      
      lockContext.strokeStyle = 'white'
      lockContext.lineWidth = 4
      lockContext.beginPath()
      lockContext.arc(64, 48, 24, 0, Math.PI * 2)
      lockContext.stroke()
      
      lockContext.beginPath()
      lockContext.rect(40, 48, 48, 48)
      lockContext.stroke()
      
      const lockTexture = new THREE.CanvasTexture(lockCanvas)
      const lockMaterial = new THREE.MeshBasicMaterial({ 
        map: lockTexture,
        transparent: true
      })
      const lockGeometry = new THREE.PlaneGeometry(1, 1)
      const lock = new THREE.Mesh(lockGeometry, lockMaterial)
      
      lock.position.set(0, 0, 0.03)
      frontCard.add(lock)
    }
  }
  
  // 定位卡片
  const xPos = (index % 3 - 1) * 2
  const yPos = -Math.floor(index / 3) * 2.5
  
  cardGroup.position.set(xPos, yPos, 0)
  
  // 添加点击事件
  setupCardClickHandler(cardGroup)
  
  // 添加出现动画
  cardGroup.scale.set(0.01, 0.01, 0.01)
  gsap.to(cardGroup.scale, {
    x: 1,
    y: 1,
    z: 1,
    duration: 0.5,
    ease: 'back.out',
    delay: index * 0.1
  })
  
  return cardGroup
}

// 设置卡片点击事件
const setupCardClickHandler = (cardGroup: THREE.Group) => {
  if (!threeScene.value) return
  
  const renderer = threeScene.value.renderer
  const camera = threeScene.value.camera
  const scene = threeScene.value.scene
  
  const raycaster = new THREE.Raycaster()
  const mouse = new THREE.Vector2()
  
  renderer.domElement.addEventListener('click', (event) => {
    // 计算鼠标在归一化设备坐标中的位置
    const rect = renderer.domElement.getBoundingClientRect()
    mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1
    
    // 更新射线投射器
    raycaster.setFromCamera(mouse, camera)
    
    // 计算与射线相交的对象
    const intersects = raycaster.intersectObjects(scene.children, true)
    
    if (intersects.length > 0) {
      let object = intersects[0].object
      
      // 查找具有cardId的父对象
      while (object && (!object.parent || !object.parent.userData || !object.parent.userData.cardId)) {
        object = object.parent as THREE.Object3D
        if (!object) break
      }
      
      if (object && object.parent && object.parent.userData && object.parent.userData.cardId) {
        const cardId = object.parent.userData.cardId
        selectCard(cardId)
      }
    }
  })
}

// 高亮选中的卡片
const highlightSelectedCard = (cardId: string) => {
  if (!threeScene.value) return
  
  const scene = threeScene.value.scene
  
  // 重置所有卡片的缩放
  scene.traverse((object) => {
    if (object.userData && object.userData.cardId) {
      gsap.to(object.scale, {
        x: 1,
        y: 1,
        z: 1,
        duration: 0.3
      })
      
      // 重置位置
      gsap.to(object.position, {
        z: 0,
        duration: 0.3
      })
    }
  })
  
  // 高亮选中的卡片
  scene.traverse((object) => {
    if (object.userData && object.userData.cardId === cardId) {
      gsap.to(object.scale, {
        x: 1.2,
        y: 1.2,
        z: 1.2,
        duration: 0.3,
        ease: 'back.out'
      })
      
      // 将卡片向前移动
      gsap.to(object.position, {
        z: 0.5,
        duration: 0.3
      })
      
      // 移动相机聚焦到选中的卡片
      const camera = threeScene.value.camera
      gsap.to(camera.position, {
        x: object.position.x,
        y: object.position.y + 1,
        z: object.position.z + 5,
        duration: 1,
        onUpdate: () => {
          camera.lookAt(object.position)
        }
      })
    }
  })
}

// 更新卡片翻转
const updateCardFlip = (cardId: string, isFlipped: boolean) => {
  if (!threeScene.value) return
  
  const scene = threeScene.value.scene
  
  scene.traverse((object) => {
    if (object.userData && object.userData.cardId === cardId) {
      gsap.to(object.rotation, {
        y: isFlipped ? Math.PI : 0,
        duration: 0.6,
        ease: 'power2.inOut'
      })
    }
  })
}

// 解锁卡片动画
const unlockCardAnimation = (card: any) => {
  if (!threeScene.value) return
  
  const scene = threeScene.value.scene
  
  // 查找卡片对象
  let cardObj = null
  scene.traverse((object) => {
    if (object.userData && object.userData.cardId === card.id) {
      cardObj = object
    }
  })
  
  if (!cardObj) return
  
  // 创建粒子效果
  const particleCount = 50
  const particles = new THREE.Group()
  
  for (let i = 0; i < particleCount; i++) {
    const particleGeometry = new THREE.SphereGeometry(0.05, 8, 8)
    const particleMaterial = new THREE.MeshStandardMaterial({ 
      color: getRarityColor(card.rarity),
      emissive: getRarityColor(card.rarity),
      emissiveIntensity: 0.5
    })
    
    const particle = new THREE.Mesh(particleGeometry, particleMaterial)
    
    // 设置初始位置（围绕卡片）
    const theta = Math.random() * Math.PI * 2
    const radius = 0.5 + Math.random() * 1.5
    
    particle.position.set(
      cardObj.position.x + Math.cos(theta) * radius,
      cardObj.position.y + Math.sin(theta) * radius,
      cardObj.position.z + (Math.random() - 0.5) * 2
    )
    
    particles.add(particle)
    
    // 添加动画 - 向卡片聚集
    gsap.to(particle.position, {
      x: cardObj.position.x,
      y: cardObj.position.y,
      z: cardObj.position.z,
      duration: 1 + Math.random(),
      ease: 'power2.in',
      delay: i * 0.02,
      onComplete: () => {
        if (i === particleCount - 1) {
          // 最后一个粒子到达后，移除所有粒子并更新卡片
          setTimeout(() => {
            scene.remove(particles)
            
            // 更新卡片外观
            scene.traverse((object) => {
              if (object.userData && object.userData.cardId === card.id) {
                // 移除旧卡片
                const parent = object.parent
                const position = object.position.clone()
                const index = Array.from(parent.children).indexOf(object)
                
                parent.remove(object)
                
                // 创建新卡片
                const newCard = createCardObject(card, index)
                newCard.position.copy(position)
                parent.add(newCard)
                
                // 添加闪光效果
                const glowGeometry = new THREE.PlaneGeometry(2, 2.5)
                const glowMaterial = new THREE.MeshBasicMaterial({ 
                  color: getRarityColor(card.rarity),
                  transparent: true,
                  opacity: 0.7,
                  side: THREE.DoubleSide
                })
                
                const glow = new THREE.Mesh(glowGeometry, glowMaterial)
                glow.position.copy(newCard.position)
                glow.position.z -= 0.1
                parent.add(glow)
                
                // 闪光动画
                gsap.to(glowMaterial, {
                  opacity: 0,
                  duration: 1.5,
                  onComplete: () => {
                    parent.remove(glow)
                  }
                })
              }
            })
          }, 500)
        }
      }
    })
    
    // 添加旋转动画
    gsap.to(particle.rotation, {
      x: Math.PI * 2 * Math.random(),
      y: Math.PI * 2 * Math.random(),
      z: Math.PI * 2 * Math.random(),
      duration: 1 + Math.random(),
      repeat: 1,
      ease: 'none'
    })
  }
  
  scene.add(particles)
  
  // 卡片动画
  scene.traverse((object) => {
    if (object.userData && object.userData.cardId === card.id) {
      // 卡片缩放动画
      gsap.to(object.scale, {
        x: 1.5,
        y: 1.5,
        z: 1.5,
        duration: 0.5,
        ease: 'back.out',
        yoyo: true,
        repeat: 1
      })
      
      // 卡片旋转动画
      gsap.to(object.rotation, {
        y: Math.PI * 4,
        duration: 2,
        ease: 'power2.inOut'
      })
    }
  })
}

// 获取稀有度颜色
const getRarityColor = (rarity: string): number => {
  switch (rarity) {
    case 'common': return 0x95a5a6
    case 'uncommon': return 0x2ecc71
    case 'rare': return 0x3498db
    case 'epic': return 0x9b59b6
    case 'legendary': return 0xf1c40f
    default: return 0x95a5a6
  }
}

// 获取稀有度颜色（十六进制字符串）
const getRarityColorHex = (rarity: string): string => {
  switch (rarity) {
    case 'common': return '#95a5a6'
    case 'uncommon': return '#2ecc71'
    case 'rare': return '#3498db'
    case 'epic': return '#9b59b6'
    case 'legendary': return '#f1c40f'
    default: return '#95a5a6'
  }
}

// 获取稀有度星级
const getRarityStars = (rarity: string): string => {
  switch (rarity) {
    case 'common': return '★'
    case 'uncommon': return '★★'
    case 'rare': return '★★★'
    case 'epic': return '★★★★'
    case 'legendary': return '★★★★★'
    default: return '★'
  }
}

// 解锁卡片
const unlockCard = (cardId: string) => {
  const card = honorStore.getCards.find(card => card.id === cardId)
  
  if (card && !card.unlocked) {
    honorStore.unlockCard(cardId)
    unlockCardAnimation(card)
  }
}

onMounted(() => {
  if (threeScene.value) {
    // 调整相机位置
    const camera = threeScene.value.camera
    camera.position.set(0, 0, 10)
    camera.lookAt(0, 0, 0)
    
    // 创建荣誉系统场景
    createHonorsScene()
  }
})
</script>

<template>
  <div class="honors-view">
    <div class="three-container">
      <ThreeScene ref="threeScene" backgroundColor="#1a1a2e" />
    </div>
    
    <div class="ui-overlay">
      <div class="header">
        <h1>荣誉系统</h1>
        <button class="back-button" @click="router.push('/')">返回首页</button>
      </div>
      
      <div class="honors-panel">
        <div class="cards-section">
          <h2>成就卡片</h2>
          
          <div class="categories">
            <div 
              v-for="category in cardCategories" 
              :key="category"
              class="category"
            >
              <h3>{{ category }}</h3>
              
              <div class="cards">
                <div 
                  v-for="card in getCardsByCategory(category)" 
                  :key="card.id"
                  class="card"
                  :class="{
                    'card-selected': selectedCardId === card.id,
                    'card-locked': !card.unlocked,
                    'card-flipped': flippedCards[card.id]
                  }"
                  @click="selectCard(card.id)"
                >
                  <div class="card-inner">
                    <div class="card-front">
                      <div class="card-header">
                        <h4>{{ card.unlocked ? card.name : '???' }}</h4>
                        <span 
                          v-if="card.unlocked" 
                          class="card-rarity"
                          :style="{ color: getRarityColorHex(card.rarity) }"
                        >
                          {{ getRarityStars(card.rarity) }}
                        </span>
                      </div>
                      
                      <div class="card-content">
                        <div v-if="card.unlocked" class="card-description">
                          {{ card.description }}
                        </div>
                        <div v-else class="card-locked-icon">
                          <i class="lock-icon">🔒</i>
                        </div>
                      </div>
                      
                      <div class="card-footer">
                        <span v-if="card.unlocked" class="card-date">
                          获得于: {{ new Date(card.unlockedAt).toLocaleDateString() }}
                        </span>
                        <span v-else class="card-locked-text">未解锁</span>
                      </div>
                    </div>
                    
                    <div class="card-back">
                      <div class="card-back-pattern">
                        <div class="card-back-logo">荣誉卡片</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="card-detail" v-if="selectedCard">
          <h2>卡片详情</h2>
          
          <div class="detail-card" :class="{ 'card-locked': !selectedCard.unlocked }">
            <div class="detail-header">
              <h3>{{ selectedCard.unlocked ? selectedCard.name : '???' }}</h3>
              <span 
                v-if="selectedCard.unlocked" 
                class="detail-rarity"
                :style="{ color: getRarityColorHex(selectedCard.rarity) }"
              >
                {{ getRarityStars(selectedCard.rarity) }}
              </span>
            </div>
            
            <div class="detail-content">
              <div v-if="selectedCard.unlocked" class="detail-description">
                {{ selectedCard.description }}
              </div>
              <div v-else class="detail-locked">
                <div class="detail-locked-icon">🔒</div>
                <p>完成指定条件解锁此卡片</p>
              </div>
            </div>
            
            <div class="detail-actions">
              <button 
                class="flip-button" 
                @click="flipCard(selectedCard.id)"
              >
                {{ flippedCards[selectedCard.id] ? '正面' : '背面' }}
              </button>
              
              <button 
                v-if="!selectedCard.unlocked" 
                class="unlock-button" 
                @click="unlockCard(selectedCard.id)"
              >
                解锁卡片
              </button>
            </div>
            
            <div class="detail-footer">
              <div v-if="selectedCard.unlocked" class="detail-stats">
                <div class="stat">
                  <span class="stat-label">获得时间:</span>
                  <span class="stat-value">
                    {{ new Date(selectedCard.unlockedAt).toLocaleString() }}
                  </span>
                </div>
                
                <div class="stat">
                  <span class="stat-label">稀有度:</span>
                  <span 
                    class="stat-value"
                    :style="{ color: getRarityColorHex(selectedCard.rarity) }"
                  >
                    {{ selectedCard.rarity }}
                  </span>
                </div>
                
                <div class="stat">
                  <span class="stat-label">分类:</span>
                  <span class="stat-value">{{ selectedCard.category }}</span>
                </div>
              </div>
              
              <div v-else class="detail-unlock-condition">
                <h4>解锁条件:</h4>
                <p>{{ selectedCard.unlockCondition }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <div class="user-progress">
          <h2>用户进度</h2>
          
          <div class="progress-section">
            <div class="progress-card">
              <h3>等级</h3>
              <div class="level-display">
                <div class="level-number">{{ honorStore.getUserLevel }}</div>
                <div class="level-title">{{ honorStore.getUserLevelTitle }}</div>
              </div>
              
              <div class="exp-bar">
                <div 
                  class="exp-progress"
                  :style="{
                    width: `${(honorStore.getUserExp % 100) / 100 * 100}%`
                  }"
                ></div>
              </div>
              
              <div class="exp-text">
                {{ honorStore.getUserExp % 100 }}/100 XP
              </div>
            </div>
            
            <div class="progress-card">
              <h3>卡片收集</h3>
              <div class="collection-stats">
                <div class="stat-item">
                  <div class="stat-value">
                    {{ honorStore.getCards.filter(card => card.unlocked).length }}
                  </div>
                  <div class="stat-label">已解锁</div>
                </div>
                
                <div class="stat-item">
                  <div class="stat-value">
                    {{ honorStore.getCards.length }}
                  </div>
                  <div class="stat-label">总数</div>
                </div>
                
                <div class="stat-item">
                  <div class="stat-value">
                    {{ Math.round(honorStore.getCards.filter(card => card.unlocked).length / honorStore.getCards.length * 100) }}%
                  </div>
                  <div class="stat-label">完成度</div>
                </div>
              </div>
              
              <div class="collection-progress">
                <div 
                  class="collection-bar"
                  :style="{
                    width: `${honorStore.getCards.filter(card => card.unlocked).length / honorStore.getCards.length * 100}%`
                  }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.honors-view {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.three-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.ui-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  overflow-y: auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  pointer-events: auto;
}

.header h1 {
  color: white;
  margin: 0;
  text-shadow: 0 0 10px rgba(155, 89, 182, 0.7);
}

.back-button {
  background-color: #8e44ad;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.back-button:hover {
  background-color: #9b59b6;
}

.honors-panel {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  flex: 1;
  pointer-events: auto;
}

.cards-section {
  flex: 2;
  min-width: 300px;
  background-color: rgba(26, 26, 46, 0.8);
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(155, 89, 182, 0.3);
}

.cards-section h2 {
  color: white;
  margin-top: 0;
  border-bottom: 2px solid #8e44ad;
  padding-bottom: 0.5rem;
}

.categories {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 1rem;
}

.category h3 {
  color: #9b59b6;
  margin: 0 0 0.5rem 0;
}

.cards {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.card {
  width: 150px;
  height: 200px;
  perspective: 1000px;
  cursor: pointer;
}

.card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.6s;
  transform-style: preserve-3d;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.card-flipped .card-inner {
  transform: rotateY(180deg);
}

.card-front, .card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 8px;
  overflow: hidden;
}

.card-front {
  background: linear-gradient(135deg, #2c3e50, #1a1a2e);
  display: flex;
  flex-direction: column;
}

.card-back {
  background: linear-gradient(135deg, #34495e, #2c3e50);
  transform: rotateY(180deg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-header {
  padding: 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card-header h4 {
  margin: 0;
  color: white;
  font-size: 0.9rem;
  text-align: center;
}

.card-rarity {
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.card-content {
  flex: 1;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-description {
  color: #ecf0f1;
  font-size: 0.8rem;
  text-align: center;
}

.card-locked-icon {
  font-size: 2rem;
  color: #7f8c8d;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.lock-icon {
  font-style: normal;
}

.card-footer {
  padding: 0.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.card-date, .card-locked-text {
  font-size: 0.7rem;
  color: #bdc3c7;
}

.card-back-pattern {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: repeating-linear-gradient(
    45deg,
    rgba(52, 73, 94, 0.5),
    rgba(52, 73, 94, 0.5) 10px,
    rgba(44, 62, 80, 0.5) 10px,
    rgba(44, 62, 80, 0.5) 20px
  );
}

.card-back-logo {
  background-color: rgba(44, 62, 80, 0.8);
  color: #ecf0f1;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: bold;
  border: 2px solid #7f8c8d;
}

.card-selected {
  transform: translateY(-10px);
  box-shadow: 0 10px 20px rgba(155, 89, 182, 0.4);
}

.card-selected .card-inner {
  box-shadow: 0 0 15px rgba(155, 89, 182, 0.7);
}

.card-locked .card-front {
  background: linear-gradient(135deg, #2c3e50, #1a1a2e);
  opacity: 0.7;
}

.card-detail {
  flex: 1;
  min-width: 300px;
  background-color: rgba(26, 26, 46, 0.8);
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(155, 89, 182, 0.3);
  display: flex;
  flex-direction: column;
}

.card-detail h2 {
  color: white;
  margin-top: 0;
  border-bottom: 2px solid #8e44ad;
  padding-bottom: 0.5rem;
}

.detail-card {
  flex: 1;
  background: linear-gradient(135deg, #2c3e50, #1a1a2e);
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  margin-top: 1rem;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.5rem;
}

.detail-header h3 {
  margin: 0;
  color: white;
}

.detail-rarity {
  font-size: 1rem;
}

.detail-content {
  flex: 1;
  margin-bottom: 1rem;
}

.detail-description {
  color: #ecf0f1;
  line-height: 1.5;
}

.detail-locked {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #7f8c8d;
}

.detail-locked-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.detail-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.flip-button, .unlock-button {
  flex: 1;
  padding: 0.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.flip-button {
  background-color: #3498db;
  color: white;
}

.flip-button:hover {
  background-color: #2980b9;
}

.unlock-button {
  background-color: #9b59b6;
  color: white;
}

.unlock-button:hover {
  background-color: #8e44ad;
}

.detail-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1rem;
}

.detail-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat {
  display: flex;
  justify-content: space-between;
}

.stat-label {
  color: #bdc3c7;
}

.stat-value {
  color: white;
}

.detail-unlock-condition {
  color: #bdc3c7;
}

.detail-unlock-condition h4 {
  margin: 0 0 0.5rem 0;
  color: #9b59b6;
}

.user-progress {
  width: 100%;
  background-color: rgba(26, 26, 46, 0.8);
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(155, 89, 182, 0.3);
  margin-top: 1rem;
}

.user-progress h2 {
  color: white;
  margin-top: 0;
  border-bottom: 2px solid #8e44ad;
  padding-bottom: 0.5rem;
}

.progress-section {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.progress-card {
  flex: 1;
  background: linear-gradient(135deg, #2c3e50, #1a1a2e);
  border-radius: 8px;
  padding: 1rem;
}

.progress-card h3 {
  color: #9b59b6;
  margin-top: 0;
  margin-bottom: 1rem;
  text-align: center;
}

.level-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1rem;
}

.level-number {
  font-size: 3rem;
  font-weight: bold;
  color: #9b59b6;
}

.level-title {
  color: #ecf0f1;
  margin-top: 0.5rem;
}

.exp-bar {
  height: 10px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  margin-bottom: 0.5rem;
  overflow: hidden;
}

.exp-progress {
  height: 100%;
  background-color: #9b59b6;
  border-radius: 5px;
}

.exp-text {
  text-align: center;
  color: #bdc3c7;
  font-size: 0.8rem;
}

.collection-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 1rem;
}

.stat-item {
  text-align: center;
}

.stat-item .stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #9b59b6;
}

.stat-item .stat-label {
  color: #bdc3c7;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.collection-progress {
  height: 10px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  overflow: hidden;
}

.collection-bar {
  height: 100%;
  background-color: #9b59b6;
  border-radius: 5px;
}
</style>