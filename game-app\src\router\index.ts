import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/HomeView.vue')
    },
    {
      path: '/tasks',
      name: 'tasks',
      component: () => import('../views/TasksView.vue')
    },
    {
      path: '/economy',
      name: 'economy',
      component: () => import('../views/EconomyView.vue')
    },
    {
      path: '/honors',
      name: 'honors',
      component: () => import('../views/HonorsView.vue')
    }
  ]
})

export default router