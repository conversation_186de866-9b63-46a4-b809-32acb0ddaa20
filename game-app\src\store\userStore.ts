import { defineStore } from 'pinia'

interface UserSettings {
  theme: 'light' | 'dark'
  soundEnabled: boolean
  notificationsEnabled: boolean
}

interface UserState {
  name: string
  avatar: string
  settings: UserSettings
}

export const useUserStore = defineStore('user', {
  state: () => ({
    name: '用户',
    avatar: '',
    settings: {
      theme: 'light' as const,
      soundEnabled: true,
      notificationsEnabled: true
    }
  }),
  getters: {
    getUserName: (state) => state.name,
    getUserAvatar: (state) => state.avatar,
    getSettings: (state) => state.settings,
    getTheme: (state) => state.settings.theme
  },
  actions: {
    updateName(name: string) {
      this.name = name
    },
    updateAvatar(avatar: string) {
      this.avatar = avatar
    },
    updateSettings(settings: Partial<UserSettings>) {
      this.settings = { ...this.settings, ...settings }
    },
    toggleTheme() {
      this.settings.theme = this.settings.theme === 'light' ? 'dark' : 'light'
    },
    toggleSound() {
      this.settings.soundEnabled = !this.settings.soundEnabled
    },
    toggleNotifications() {
      this.settings.notificationsEnabled = !this.settings.notificationsEnabled
    }
  },
  persist: {
    enabled: true,
    strategies: [
      {
        key: 'user',
        storage: localStorage
      }
    ]
  }
})