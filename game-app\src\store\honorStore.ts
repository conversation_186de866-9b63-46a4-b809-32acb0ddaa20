import { defineStore } from 'pinia'

interface Card {
  id: string
  name: string
  description: string
  category: string
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
  requiredLevel: number
  requiredTasks?: string[] // 需要完成的任务ID列表
  isUnlocked: boolean
  imageUrl?: string
}

interface UserProfile {
  experience: number
  level: number
  unlockedCards: string[] // 已解锁卡片的ID列表
}

export const useHonorStore = defineStore('honor', {
  state: () => ({
    cards: [] as Card[],
    userProfile: {
      experience: 0,
      level: 1,
      unlockedCards: []
    } as UserProfile
  }),
  getters: {
    getCards: (state) => state.cards,
    getUnlockedCards: (state) => {
      return state.cards.filter(card => card.isUnlocked)
    },
    getLockedCards: (state) => {
      return state.cards.filter(card => !card.isUnlocked)
    },
    getCardsByCategory: (state) => (category: string) => {
      return state.cards.filter(card => card.category === category)
    },
    getCardsByRarity: (state) => (rarity: Card['rarity']) => {
      return state.cards.filter(card => card.rarity === rarity)
    },
    getUserLevel: (state) => state.userProfile.level,
    getUserExperience: (state) => state.userProfile.experience,
    getExperienceToNextLevel: (state) => {
      // 简单的经验值计算公式，可以根据需要调整
      return state.userProfile.level * 100
    }
  },
  actions: {
    addCard(card: Omit<Card, 'id' | 'isUnlocked'>) {
      const newCard: Card = {
        ...card,
        id: Date.now().toString(),
        isUnlocked: false
      }
      this.cards.push(newCard)
    },
    updateCard(id: string, updates: Partial<Card>) {
      const index = this.cards.findIndex(card => card.id === id)
      if (index !== -1) {
        this.cards[index] = { ...this.cards[index], ...updates }
      }
    },
    deleteCard(id: string) {
      this.cards = this.cards.filter(card => card.id !== id)
      // 如果卡片已解锁，从用户解锁列表中移除
      if (this.userProfile.unlockedCards.includes(id)) {
        this.userProfile.unlockedCards = this.userProfile.unlockedCards.filter(cardId => cardId !== id)
      }
    },
    unlockCard(id: string) {
      const index = this.cards.findIndex(card => card.id === id)
      if (index !== -1 && !this.cards[index].isUnlocked) {
        this.cards[index].isUnlocked = true
        this.userProfile.unlockedCards.push(id)
        return true
      }
      return false
    },
    addExperience(amount: number) {
      this.userProfile.experience += amount
      
      // 检查是否可以升级
      this.checkLevelUp()
      
      // 检查是否可以解锁新卡片
      this.checkCardUnlocks()
    },
    checkLevelUp() {
      const experienceToNextLevel = this.getExperienceToNextLevel
      
      while (this.userProfile.experience >= experienceToNextLevel) {
        this.userProfile.experience -= experienceToNextLevel
        this.userProfile.level += 1
      }
    },
    checkCardUnlocks() {
      // 检查是否有可以解锁的卡片
      this.cards.forEach(card => {
        if (!card.isUnlocked && card.requiredLevel <= this.userProfile.level) {
          // 如果有特定任务要求，需要检查任务是否完成
          if (!card.requiredTasks || card.requiredTasks.length === 0) {
            this.unlockCard(card.id)
          }
          // 任务完成检查逻辑需要与任务系统集成
        }
      })
    },
    resetProgress() {
      this.userProfile = {
        experience: 0,
        level: 1,
        unlockedCards: []
      }
      
      // 重置所有卡片的解锁状态
      this.cards.forEach((card, index) => {
        this.cards[index].isUnlocked = false
      })
    }
  }
})